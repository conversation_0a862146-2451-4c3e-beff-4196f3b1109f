import { Component, Input, OnInit, Output, EventEmitter, SimpleChanges, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { OrderStatus, CheckTypeEnum } from '../../garment-outsourcing/models/order-garment-outsourcing.enum';
import { OrderOutsourcingService } from '../order-outsourcing.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { FormGroup, Validators } from '@angular/forms';
import { FlcDynamicSearchSelectComponent, FlcUtilService } from 'fl-common-lib';
import { ChangeDetectorRef } from '@angular/core';
import { SecProcessDetailInfoInterface } from '../../sec-process-outsourcing/model/sec-process-outsourcing.interface';
import { BulkService } from 'src/app/core/order/bulk/bulk.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ActivatedRoute } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { RecommendFactoryService } from 'fl-sewsmart-lib/recommend-factory';
import { SysSettingsServices } from 'fl-sewsmart-lib/common-services';
import { IoBasic } from '../models/order-outsourcing-interface';
import { StageTypeEnum } from '../models/order-outsourcing.enum';

@Component({
  selector: 'app-order-outsourcing-factory-item-header',
  templateUrl: './order-outsourcing-factory-item-header.component.html',
  styleUrls: ['./order-outsourcing-factory-item-header.component.scss'],
})
export class OrderOutsourcingFactoryItemHeaderComponent implements OnInit {
  @ViewChild('factorySelectRef') factorySelectRef?: FlcDynamicSearchSelectComponent;
  @Input() toggle = true;
  @Input() parentGroup!: FormGroup;
  @Input() editMode!: 'add' | 'edit' | 'read'; // 当前页面编辑状态
  @Input() _pos: any = []; // 组件内部用于渲染tabs,即用户为加工厂添加的交付单
  @Input() orderStatus!: number;
  @Input() detailForm!: FormGroup;
  @Input() orderDetail!: SecProcessDetailInfoInterface;
  @Input() showOrderFinish = true; // 成衣外发设置权限时，是否展示订单完成按钮
  @Input() showOneClickInbound = false; // 成衣外发设置权限时，是否展示一键入库按钮
  @Input() showAdvancePayment = false; // 成衣外发设置权限时，是否展示预付款按钮
  @Input() io_basic?: IoBasic;
  @Output() handleChangeToggle = new EventEmitter();
  @Output() handleChangeValue = new EventEmitter();
  @Output() handleChangeLineValue = new EventEmitter();
  @Output() handleRemoveFactory = new EventEmitter();
  @Output() orderFinish = new EventEmitter();
  @Output() showInferencePrice = new EventEmitter();
  @Output() oneClickInbound = new EventEmitter(); // 一键入库
  @Output() onAdvancePayment = new EventEmitter(); // 预付款
  orderStatusEnum = OrderStatus;
  searchOptionFetchUrl = this.outsourcingService.plantOptions;
  factory_type = this.outsourcingService.factory_type;
  checkTypeEnum = CheckTypeEnum;

  private factory_options: any[] = [];
  line_list: any[] = []; //产线下拉
  order_fatory_option: any[] = [];
  private _sysSubscription?: Subscription;

  check_type_text = {
    [CheckTypeEnum.allCheck]: '全检',
    [CheckTypeEnum.partCheck]: '抽检',
    [CheckTypeEnum.noCheck]: '免检',
  };

  checkTypeOptions: any[] = [];
  merchandiserOptions: any[] = [];
  onlyCanSelectOrderOption = false;
  showLine = false; // 是否显示产线

  searchPartOptionsFetchUrl = this.outsourcingService.partOptions; // 部位下拉url
  dictOptionsUrl = this.outsourcingService.dictOptionsUrl; // 数据字典下拉url
  _garmentEventEmitter!: Subscription;

  part_names_arr: any[] = []; // 处理部位

  constructor(
    private outsourcingService: OrderOutsourcingService,
    private nzModalService: NzModalService,
    public flcUtilService: FlcUtilService,
    private cd: ChangeDetectorRef,
    private _service: BulkService,
    private _message: NzMessageService,
    private _route: ActivatedRoute,
    private http: HttpClient,
    private _recommendFactoryService: RecommendFactoryService,
    private _systemSettingService: SysSettingsServices
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    const { parentGroup } = changes;
    if (changes.editMode && changes.editMode.currentValue && changes.editMode.currentValue === 'edit') {
      this.sampleShow();
    }
    if (parentGroup && parentGroup.currentValue) {
      this.getFactoryOptions();
    }
  }

  ngOnInit() {
    // 成衣外发，获取大货工段下拉数据
    if (this.factory_type === 1) {
      this.getStageOptionList();
    }
    this.sampleShow();
    //初始化检验类型下拉数据，如果已经开始检验，免检置灰
    this.checkTypeOptions = Object.entries(this.check_type_text).map((item) => {
      const obj = { value: Number(item[0]), label: item[1], disabled: false };
      if (Number(item[0]) === CheckTypeEnum.noCheck) {
        obj.disabled = !!this.parentGroup.get('check_has_started')?.value;
      }
      return obj;
    });

    this._sysSubscription = this._systemSettingService.boardcast.subscribe((result: any) => {
      this.onlyCanSelectOrderOption = result.order?.distributionForceFactory || false;
    });

    this._garmentEventEmitter = this.outsourcingService.garmentEventEmitter.subscribe((res) => {
      if (res.onchange === 'outsourcingQtyChange') {
        // 成衣外发，外发数量变化， 需重新计算预付款金额
        console.log('外发数量变化');
        this.factory_type === 1 && this.calculatePrepaymentsAmount();
      }
    });

    this.part_names_arr = this.parentGroup.get('part_names')?.value?.map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });
  }

  ngOnDestroy(): void {
    this._sysSubscription?.unsubscribe();
    this._garmentEventEmitter?.unsubscribe();
  }

  compareFn = (o1: any, o2: any): boolean => (o1 && o2 ? o1.value === o2.value : o1 === o2);

  // async getOptions() {
  //   this._service.getFactorysIds().subscribe((req) => {
  //     if (req) {
  //       this.factory_options = req.data.option_list || [];
  //       const _op = this.factory_options.find((f: any) => f.label === this.parentGroup.value.factory_short_name);
  //       this.line_list = _op?.line_list || [{ label: '1223', value: 1 }]; // 赋值产线
  //       // 查看 订单需求是否设置加工厂
  //       if (this.io_basic?.process_factory_name) {
  //         const _op = this.factory_options.find((f: any) => f.label === this.io_basic?.process_factory_name);
  //         this.order_fatory_option = _op ? [_op] : [];
  //         if (!this.parentGroup.get('factory_short_name')?.value) {
  //           this.parentGroup.get('factory_short_name')?.setValue(_op?.label);
  //         }
  //       }
  //       this._service.getMerchandiser({ factory_id: _op?.id }).subscribe((res) => {
  //         this.merchandiserOptions = res.data.employees;
  //       });
  //     }
  //   });
  // }
  priceChange() {
    const rate = this.flcUtilService.accDiv(this.parentGroup.get('tax_rate')?.value || '0', 100);
    const percent = this.flcUtilService.accAdd(1, rate);
    const tax_price = String(this.flcUtilService.accMul(this.parentGroup.get('unit_price')?.value || '0', percent));
    this.parentGroup.get('tax_price')?.setValue(tax_price);

    // 计算预付款金额
    this.calculatePrepaymentsAmount();
  }
  _handleChangeToggle() {
    this.handleChangeToggle.emit();
  }

  _handleChangeValue(event: any) {
    this.onChangeFactoryValueEmit();
    this.parentGroup.get('line_no')?.setValue(null);
    this.parentGroup.get('line_name')?.setValue(null);
    // 清空付款方式和结算方式，并重新带入
    this.parentGroup.get('payment_method')?.setValue(event?.selectLine?.payment_method ?? null);
    this.parentGroup.get('settlement_method')?.setValue(event?.selectLine?.settlement_method ?? null);
    this.parentGroup.get('payment_method_id')?.setValue(event?.selectLine?.payment_method_id ?? null);
    this.parentGroup.get('settlement_method_id')?.setValue(event?.selectLine?.settlement_method_id ?? null);
    this.handleChangeValue.emit(event);
  }

  onChangeFactory(event: any) {
    const _option = this.factory_options.find((f: any) => f.label === event);
    const _data = {
      selectLine: _option,
      value: event,
      data: {
        option_list: [_option],
      },
    };
    this.onChangeFactoryValueEmit();
    this.handleChangeValue.emit(_data);
  }

  _handleRemoveFactory() {
    this.handleRemoveFactory.emit();
  }

  onChangeLine(e: string) {
    const _op = this.line_list.find((item) => item.line_no === e);
    this.parentGroup.get('line_name')?.setValue(_op?.line_name);
    this.handleChangeLineValue.emit();
  }

  /* 选择检验方式，选择免检时移除非空验证 */
  checkTypeChange(val: any) {
    switch (val) {
      case CheckTypeEnum.allCheck:
        this.parentGroup.get('check_radio')?.reset(100);
        break;
      case CheckTypeEnum.partCheck:
        this.parentGroup.get('check_radio')?.reset(null);
        break;
      case CheckTypeEnum.noCheck:
        this.parentGroup.get('check_radio')?.reset(null);
        break;
    }
    this.cd.detectChanges();
  }

  /** 判断重新自动打样按钮是否展示 */
  sample_show_factory: any = {}; // 当前外发单所有工厂的自动打样按钮显示状态
  sampleShow() {
    if (this.editMode !== 'read') return;
    const order_id = this._route.snapshot.paramMap.get('id') || '';
    this.http.get<any>(`/service/scm/sample_order/sample_show/${order_id}`).subscribe((res: any) => {
      res?.data?.forEach((d: any) => {
        this.sample_show_factory[d.sample_factory_code] = d.result;
      });
    });
  }

  /** 重新自动打样接口 */
  autoSample() {
    const payload = {
      io_out_sourcing_id: this.orderDetail['id'],
      sample_factory: [
        {
          code: this.parentGroup?.value?.factory_code,
          name: this.parentGroup?.value?.factory_short_name,
        },
      ],
    };

    return this.http.post<any>('/service/scm/sample_order/auto_sample', payload).subscribe(() => {
      this._message.success('操作成功');
    });
  }

  isRecommendLoading = false;
  async onSelectFactroy() {
    this.isRecommendLoading = true;
    await new Promise((resolve) => setTimeout(resolve, 1000));
    this.isRecommendLoading = false;

    const _payload = {
      extra: {
        order_uuid: this.outsourcingService.io_uuid,
      },
    };
    if (!(await this._recommendFactoryService.checkRecommendRules(_payload))) return;
    this._recommendFactoryService
      .openDrawer({ isMultiple: false, orderType: 3 }, _payload, [this.parentGroup.get('factory_short_name')?.value])
      .subscribe((res: any) => {
        if (res) {
          this.parentGroup.get('factory_short_name')?.setValue(res.factory_name);
          this.factorySelectRef?.onSearch(res.factory_name);
        }
      });
  }

  getFactoryOptions() {
    this._service.getFactorysIds().subscribe((req) => {
      if (req) {
        this.factory_options = req.data.option_list || [];
        this.onChangeFactoryValueEmit();
      }
    });
  }

  private onChangeFactoryValueEmit() {
    // 查找工厂选项
    const _op = this.factory_options.find((f) => f.label === this.parentGroup.value.factory_short_name);

    // 赋值产线
    this.line_list = _op?.line_list || [];
    this.showLine = _op?.order_distribute_type === 2;

    if (this.showLine) {
      this.parentGroup.get('line_no')?.addValidators(Validators.required);
    } else {
      this.parentGroup.get('line_no')?.removeValidators(Validators.required);
    }

    // 查看订单需求是否设置加工厂
    if (this.io_basic?.process_factory_name) {
      const processFactory = this.factory_options.find((f) => f.label === this.io_basic?.process_factory_name);
      this.order_fatory_option = processFactory ? [processFactory] : [];

      // if (!this.parentGroup.get('factory_short_name')?.value) {
      //   this.parentGroup.get('factory_short_name')?.setValue(processFactory?.label);
      // }
    }

    // 获取跟单员
    this._service.getMerchandiser({ factory_id: _op?.id }).subscribe({
      next: (res) => {
        this.merchandiserOptions = res.data.employees;
        const merchandiser = this.merchandiserOptions?.find((e: any) => e.value === this.parentGroup.get('merchandiser_id')?.value?.value);
        this.parentGroup.get('merchandiser_id')?.setValue(merchandiser);
        const qc = this.merchandiserOptions?.find((e: any) => e.value === this.parentGroup.get('qc_id')?.value?.value);
        this.parentGroup.get('qc_id')?.setValue(qc);
      },
      error: (error) => {
        console.error('Error fetching merchandiser:', error);
      },
    });
  }

  /*********** 成衣外发 ***********/

  // 修改工段，选择的工段按照下拉的顺序展示和传值
  _handleChangeStage(event: any) {
    const selectedValues = event;
    // 按照原始选项列表的顺序对选中的工段进行排序
    const sortedSelectedValues = this.stageOptionList?.filter((option: any) => selectedValues?.includes(option.stage_id));

    // 更新表单控件的值
    const stage_ids = sortedSelectedValues?.map((option: any) => option.stage_id);
    this.parentGroup.get('stage_ids')?.setValue(stage_ids, { emitViewToModelChange: false });
    // 更新stage_list的值
    const stage_list = sortedSelectedValues?.map((option: any) => {
      return {
        id: null,
        stage_id: option.stage_id,
        stage_name: option.stage_name,
        stage_type: option.stage_type,
      };
    });
    this.parentGroup.get('stage_list')?.setValue(stage_list);

    // 修改工段，修改后的工段无二次工艺类型， 那部位需要清空并置灰
    const include_extra_stage = sortedSelectedValues?.some((item: any) =>
      [StageTypeEnum.extra, StageTypeEnum.orderExtra].includes(item?.stage_type)
    );
    if (!include_extra_stage) {
      this.parentGroup.get('part_names')?.setValue(null);
    }
  }

  // 部位是否可编辑: 工段中只要包含二次工艺类型的工段， 就可以选择部位， 否则， 部位不可选择
  get partDisabled() {
    const stage_value = this.parentGroup.get('stage_list')?.value;
    const include_extra_stage = stage_value?.some((item: any) =>
      [StageTypeEnum.extra, StageTypeEnum.orderExtra].includes(item?.stage_type)
    );
    return !stage_value?.length || !include_extra_stage;
  }

  /***************** 成衣外发 预付款金额=含税单价*外发数量*付款比例；维护其中一个，另一个根据计算公式自动算出来 *****************/
  // 编辑预付款比例，自动计算出预付款金额
  changePrepaymentsProportion() {
    this.calculatePrepaymentsAmount();
  }

  // 编辑预付款金额，自动计算出预付款比例
  changePrepaymentsAmount() {
    this.calculatePrepaymentsProportion();
  }

  // 计算预付款金额 = 含税单价*外发数量*付款比例
  calculatePrepaymentsAmount() {
    if (this.factory_type !== 1) return;
    // 该加工厂下的数据
    const parentGroupValue = this.parentGroup.getRawValue();
    // 含税单价
    const tax_price = this.parentGroup.get('tax_price')?.value;
    // 外发数量
    const qty = (parentGroupValue.lines || [])?.reduce((prev: number, curr: any) => {
      return this.flcUtilService.accAdd(prev, Number(curr.qty ?? 0));
    }, 0);
    // 预付款比例
    const advance_payment_ratio = this.parentGroup.get('advance_payment_ratio')?.value;
    const advance_payment_money = this.flcUtilService.toFixed(
      this.flcUtilService.accMulPlus(tax_price ?? 0, qty ?? 0, this.flcUtilService.accDiv(advance_payment_ratio ?? 0, 100)),
      2
    );
    this.parentGroup.get('advance_payment_money')?.setValue(advance_payment_money, { emitViewToModelChange: false });
  }

  // 计算预付款比例 = 预付款金额/含税单价/外发数量
  calculatePrepaymentsProportion() {
    if (this.factory_type !== 1) return;
    // 该加工厂下的数据
    const parentGroupValue = this.parentGroup.getRawValue();
    // 含税单价
    const tax_price = this.parentGroup.get('tax_price')?.value;
    // 外发数量
    const qty = (parentGroupValue.lines || [])?.reduce((prev: number, curr: any) => {
      return this.flcUtilService.accAdd(prev, Number(curr.qty ?? 0));
    }, 0);
    // 预付款比例
    const advance_payment_money = this.parentGroup.get('advance_payment_money')?.value;
    // 预付款比例 =
    const advance_payment_ratio = this.flcUtilService.accDivPlus(advance_payment_money ?? 0, tax_price ?? 0, qty ?? 0);
    this.parentGroup
      .get('advance_payment_ratio')
      ?.setValue(this.flcUtilService.toFixed(this.flcUtilService.accMul(advance_payment_ratio, 100), 2), {
        emitViewToModelChange: false,
      });
  }

  // 格式化工段
  formatterStage() {
    const stage_list = this.parentGroup.get('stage_list')?.value?.map((item: any) => {
      return item?.stage_name;
    });
    return stage_list?.length ? stage_list?.join('、') : null;
  }

  // 付款方式、结算方式变化, 给对应的name赋值
  dynamicSearchChange(e: any, key: 'settlement_method_id' | 'payment_method_id') {
    if (key === 'settlement_method_id') {
      this.parentGroup.get('settlement_method')?.setValue(e?.selectLine?.label);
    }
    if (key === 'payment_method_id') {
      this.parentGroup.get('payment_method')?.setValue(e?.selectLine?.label);
    }
  }

  // 大货工段下拉列表
  stageOptionList: Array<{ stage_id: number; stage_name: string; stage_type: StageTypeEnum }> = [];
  getStageOptionList() {
    this.outsourcingService.getStageOptions({ io_id: this.detailForm?.value?.io_id }).subscribe((res) => {
      if (res?.code === 200) {
        this.stageOptionList = res?.data?.stage_list?.map((option: any) => {
          return {
            ...option,
            stage_id: option.id,
          };
        });
        // 默认全部工段
        if (this.orderDetail?.status === 1 && !this.parentGroup?.get('stage_ids')?.value?.length) {
          this._handleChangeStage(this.stageOptionList?.map((item: any) => item?.stage_id));
        }
      }
    });
  }
}
