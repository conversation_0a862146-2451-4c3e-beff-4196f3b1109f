/*
 * File: supplementary.interface.ts
 * Project: elan-web
 * File Created: Tuesday, 6th September 2022 4:02:14 pm
 * Author: liucp
 * Description:
 * -----
 * Last Modified: Tuesday, 6th September 2022 4:43:29 pm
 * Modified By: liucp
 */

import { OrderStatus } from './bulk.enum';

export interface detailModel {
  io_basic: {
    id: number;
    commit: boolean;
    io_code: string;
    customer: string;
    customer_style: string;
    category: string;
    first_material_name: string;
    first_material_code: string;
    first_material_id: number;
    second_material_name: string;
    second_material_code: string;
    second_material_id: number;
    third_material_id: number;
    third_material_name: string;
    third_material_code: string;
    order_date: string;
    contract_number: string;
    style?: string;
    extra_process_info: [
      {
        extra_process_id: number;
        extra_process_name: string;
        position_list: [
          {
            position_id: number;
            position_name: string;
          }
        ];
        deletable: boolean;
      }
    ];
    remark: string;
    order_pictures: [
      {
        name: string;
        url: string;
      }
    ];
    appendix_requirements: [
      {
        name: string;
        url: string;
      }
    ];
    customer_io_code: string;
    deletable: boolean;
    order_status: OrderStatus;
    order_status_value: string;
    reason: string;
  };
  pos: [
    {
      editable: boolean;
      po_basic: {
        id: number;
        po_code: string;
        due_time: string;
        receiver: string;
        contact: string;
        country_id: number;
        province_id: number;
        city_id: number;
        district_id: number;
        country_name: string;
        province_name: string;
        city_name: string;
        district_name: string;
        address: string;
        indexing: number;
        deletable: true;
        spec_group_id: number;
        spec_group_name: string;
      };
      po_lines: LinesType[];
    }
  ];
}

export interface LinesType {
  id: number;
  color_info: {
    color_id: number;
    color_name: string;
    color_code: string;
    color_hex: string;
  };
  size_info: {
    spec_id: number;
    spec_size: string;
    spec_code: string;
  };
  qty: number;
  indexing: number;
  deletable: true;

  unit_price: number;
  sku_amount: number | string;
  excess_rate: number | string;
  excess_quantity: number;
  deficiency_rate: number | string;
  deficiency_quantity: number;
}
export interface processItem {
  color: string;
  size: string;
  qty: number;
  unit_price: number;
  sku_amount: number;
  excess_rate: number | string;
  excess_quantity: number;
  deficiency_rate: number | string;
  deficiency_quantity: number;
  // Properties to help with rowspan
  colorRowspan?: number;
  unitPriceRowspan?: number;
  originalLine: LinesType;
  // 新增属性：颜色和尺码选择器的值
  colorCascaderValue?: string[];
  sizeSelectValue?: string[];
  // 🔥 新增属性：用于排序和索引
  originalIndex?: number; // 在原始数组中的索引
  colorFirstAppearanceIndex?: number; // 颜色首次出现的顺序索引
}
