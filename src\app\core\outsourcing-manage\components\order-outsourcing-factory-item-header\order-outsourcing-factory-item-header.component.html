<!-- 成衣外发 -->
<div
  *ngIf="factory_type === 1"
  class="plant-item-header"
  [ngClass]="{ 'plant-item-header-line': toggle, 'reset-padding': !toggle }"
  [hidden]="editMode === 'read' && _pos.length === 0 && !parentGroup.value['factory_short_name']">
  <div nz-row class="plant-item-header-left" [formGroup]="parentGroup">
    <nz-form-item class="form-item form-item-plant">
      <nz-form-label class="info-label">{{ 'outsourcingTableHeaderAndLabel.加工厂' | translate }} </nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" [flcErrorTip]="'outsourcingTableHeaderAndLabel.加工厂' | translate">
        <div style="display: flex; align-items: center" *ngIf="!onlyCanSelectOrderOption || !order_fatory_option.length; else selectTpl">
          <div style="flex: 1">
            <flc-dynamic-search-select
              #factorySelectRef
              class="plant-select-width"
              [disabled]="parentGroup.get('deletable')?.value === false || io_basic?.is_use_plan"
              [canClear]="false"
              [dataUrl]="searchOptionFetchUrl"
              [transData]="{ value: 'label', label: 'label' }"
              [defaultValue]="{ label: parentGroup.value['factory_short_name'], value: parentGroup.value['factory_short_name'] }"
              [formControlName]="'factory_short_name'"
              [column]="'factory_name'"
              [minWidth]="160"
              [isImmediately]="true"
              [payLoad]="{ factory_type: factory_type }"
              (handleSearch)="_handleChangeValue($event)">
            </flc-dynamic-search-select>
          </div>
          <button
            [hidden]="parentGroup.get('deletable')?.value === false || io_basic?.is_use_plan"
            nz-button
            flButton="pretty-primary"
            class="recommend-btn"
            (click)="onSelectFactroy()"
            style="padding: 4px 8px"
            [nzLoading]="isRecommendLoading">
            {{ isRecommendLoading ? '推荐中…' : '智能推荐' }}
          </button>
        </div>
        <ng-template #selectTpl>
          <nz-select
            [disabled]="parentGroup.get('deletable')?.value === false"
            style="min-width: 160px"
            [formControlName]="'factory_short_name'"
            (ngModelChange)="onChangeFactory($event)">
            <nz-option *ngFor="let item of order_fatory_option" [nzValue]="item.label" [nzLabel]="item.label"></nz-option>
            <nz-option
              [nzValue]="parentGroup.value['factory_short_name']"
              [nzLabel]="parentGroup.value['factory_short_name']"
              [nzHide]="true"></nz-option>
          </nz-select>
        </ng-template>
      </nz-form-control>
      <flc-text-truncated *ngIf="editMode === 'read'" [data]="parentGroup.value['factory_short_name']"></flc-text-truncated>
      <div
        class="status-area-1"
        *ngIf="[orderStatusEnum.toModifyAudit, orderStatusEnum.auditPass, orderStatusEnum.modifyAuditReturn].includes(orderStatus)">
        <div
          class="plant-status-box"
          [ngClass]="{
            'is-active': parentGroup.get('deletable')?.value === false,
            'is-finish': parentGroup.get('finish')?.value === true
          }">
          <ng-container *ngIf="parentGroup.get('finish')?.value === true; else plantStatusTpl">
            <div class="plant-status-text">{{ 'outsourcingComponents.已完成' | translate }}</div>
          </ng-container>
          <ng-template #plantStatusTpl>
            <ng-container *ngIf="parentGroup.get('deletable')?.value === false">
              <div class="plant-status-text">{{ 'outsourcingComponents.已接单' | translate }}</div>
            </ng-container>
            <ng-container *ngIf="parentGroup.get('deletable')?.value === true">
              <div class="plant-status-text">{{ 'outsourcingComponents.未接单' | translate }}</div>
            </ng-container>
          </ng-template>
        </div>
      </div>
    </nz-form-item>

    <!-- 工段 -->
    <nz-form-item class="form-item form-item-plant order-garment-outsourcing-select-area">
      <nz-form-label class="info-label" nzRequired>工段</nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="工段">
        <nz-select
          nzAllowClear
          nzShowSearch
          nzMode="multiple"
          formControlName="stage_ids"
          [nzPlaceHolder]="'请选择'"
          (ngModelChange)="_handleChangeStage($event)">
          <nz-option *ngFor="let item of stageOptionList" [nzValue]="item.stage_id" [nzLabel]="item.stage_name"></nz-option>
        </nz-select>
      </nz-form-control>
      <nz-form-control *ngIf="editMode === 'read'">
        <flc-text-truncated [data]="formatterStage()"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>

    <!-- 部位: 工段中选择了二次工艺类型的工段，才支持选择部位-->
    <nz-form-item class="form-item form-item-plant order-garment-outsourcing-select-area">
      <nz-form-label class="info-label">部位</nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="部位">
        <flc-dynamic-search-select
          [disabled]="partDisabled"
          [cpnMode]="'multiple'"
          [optAlwaysReload]="true"
          [dataUrl]="searchPartOptionsFetchUrl"
          [transData]="{ value: 'label', label: 'label' }"
          [column]="'position_name'"
          [formControlName]="'part_names'"
          [defaultValue]="part_names_arr">
        </flc-dynamic-search-select>
      </nz-form-control>
      <nz-form-control *ngIf="editMode === 'read'">
        <flc-text-truncated [data]="parentGroup.get('part_names')?.value?.join('、')"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>

    <!-- 大货单号 -->
    <ng-container>
      <nz-form-item class="form-item form-item-plant">
        <nz-form-label class="info-label" nzRequired>大货单号</nz-form-label>
        <!-- *ngIf="editMode !== 'read' && parentGroup.get('deletable')?.value === true && io_basic?.is_use_plan == false" 原逻辑， 有历史bug, 和产品(shaoshao)沟通， 暂先都可修改 -->
        <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="大货单号">
          <input nzAllowClear nz-input [formControlName]="'bulk_code'" placeholder="请选择" />
        </nz-form-control>
        <nz-form-control *ngIf="editMode === 'read'">
          <flc-text-truncated [data]="parentGroup.get('bulk_code')?.value"></flc-text-truncated>
        </nz-form-control>
      </nz-form-item>
    </ng-container>

    <!-- 产线 -->
    <ng-container *ngIf="showLine">
      <nz-form-item class="form-item form-item-plant">
        <nz-form-label class="info-label" nzRequired>产线</nz-form-label>
        <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="产线">
          <nz-select
            nzAllowClear
            nzShowSearch
            formControlName="line_no"
            [nzPlaceHolder]="'请选择'"
            (ngModelChange)="onChangeLine($event)"
            [nzDisabled]="io_basic?.is_use_plan">
            <nz-option *ngFor="let item of line_list" [nzValue]="item.line_no" [nzLabel]="item.line_name"></nz-option>
          </nz-select>
        </nz-form-control>
        <nz-form-control *ngIf="editMode === 'read'">
          <flc-text-truncated [data]="parentGroup.get('line_name')?.value"></flc-text-truncated>
        </nz-form-control>
      </nz-form-item>
    </ng-container>
    <nz-form-item class="form-item form-item-plant" *ngIf="editMode !== 'read' || (parentGroup.get('unit_price')?.value ?? '0') !== '0'">
      <nz-form-label [nzRequired]="factory_type === 1" class="info-label"
        >{{ 'outsourcingTableHeaderAndLabel.单价' | translate }}

        <span
          *ngIf="editMode !== 'read'"
          class="price-button"
          [ngClass]="{ 'price-button-disabled': !parentGroup.get('factory_short_name')?.value }">
        </span>
      </nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="">
        <nz-input-number-group nzAddOnAfter="元">
          <nz-input-number
            [nzPrecision]="5"
            [nzMin]="0"
            [nzMax]="*********.99999"
            [nzPlaceHolder]="'placeholder.input' | translate"
            (ngModelChange)="priceChange()"
            [formControlName]="'unit_price'"></nz-input-number>
        </nz-input-number-group>
      </nz-form-control>
      <flc-text-truncated *ngIf="editMode === 'read'" [data]="parentGroup.get('unit_price')?.value + '元'"></flc-text-truncated>
    </nz-form-item>
    <nz-form-item class="form-item form-item-plant" *ngIf="editMode !== 'read' || (parentGroup.get('tax_rate')?.value ?? '0') !== '0'">
      <nz-form-label [nzRequired]="factory_type === 1" class="info-label"
        >{{ 'outsourcingTableHeaderAndLabel.税率' | translate }}
        <span
          *ngIf="editMode !== 'read'"
          class="price-button"
          [ngClass]="{ 'price-button-disabled': !parentGroup.get('factory_short_name')?.value }">
        </span>
      </nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="">
        <nz-input-number-group nzAddOnAfter="%">
          <nz-input-number
            [nzPrecision]="0"
            [nzMin]="0"
            [nzMax]="*********"
            [nzPlaceHolder]="'placeholder.input' | translate"
            (ngModelChange)="priceChange()"
            [formControlName]="'tax_rate'"></nz-input-number>
        </nz-input-number-group>
      </nz-form-control>
      <flc-text-truncated *ngIf="editMode === 'read'" [data]="parentGroup.get('tax_rate')?.value + '%'"></flc-text-truncated>
    </nz-form-item>
    <nz-form-item class="form-item form-item-plant" *ngIf="editMode !== 'read' || (parentGroup.get('tax_price')?.value ?? '0') !== '0'">
      <nz-form-label [nzRequired]="factory_type === 1" class="info-label"
        >{{ 'outsourcingTableHeaderAndLabel.含税单价' | translate }}
        <span *ngIf="editMode !== 'read'" (click)="showInferencePrice.emit()" class="price-button price-button-disabled">
          (<span>{{ 'outsourcingTableHeaderAndLabel.参考价' | translate }}</span
          >)
        </span>
      </nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="">
        <nz-input-number-group nzAddOnAfter="元">
          <nz-input-number
            [nzPrecision]="5"
            [nzMin]="0"
            [nzMax]="*********.99999"
            [nzDisabled]="true"
            [nzPlaceHolder]="'placeholder.input' | translate"
            [formControlName]="'tax_price'"></nz-input-number>
        </nz-input-number-group>
      </nz-form-control>
      <flc-text-truncated *ngIf="editMode === 'read'" [data]="parentGroup.get('tax_price')?.value + '元'"></flc-text-truncated>
    </nz-form-item>
    <ng-container *ngIf="editMode !== 'read'">
      <nz-form-item class="form-item form-item-plant" style="margin-right: 4px">
        <nz-form-label class="info-label">{{ 'outsourcingTableHeaderAndLabel.检验方式' | translate }} </nz-form-label>
        <nz-form-control style="flex: none">
          <nz-select
            class="select-round"
            [formControlName]="'check_type'"
            [nzPlaceHolder]="'placeholder.select' | translate"
            (ngModelChange)="checkTypeChange($event)"
            [ngClass]="{
              'select-round-color-1': parentGroup.get('check_type')?.value === checkTypeEnum.allCheck,
              'select-round-color-2': parentGroup.get('check_type')?.value === checkTypeEnum.partCheck,
              'select-round-color-3': parentGroup.get('check_type')?.value === checkTypeEnum.noCheck
            }">
            <nz-option
              [nzValue]="item.value"
              [nzLabel]="item.label"
              [nzDisabled]="item.disabled"
              *ngFor="let item of checkTypeOptions"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="form-item form-item-plant" [hidden]="parentGroup.get('check_type')?.value === checkTypeEnum.noCheck">
        <nz-form-control *ngIf="parentGroup.get('check_type')?.value !== checkTypeEnum.allCheck" style="max-width: 300px">
          <nz-input-number-group nzAddOnAfter="%" class="select-round">
            <nz-input-number
              [nzPrecision]="1"
              [nzMin]="0.1"
              [nzMax]="99.9"
              [nzPlaceHolder]="'outsourcingTableHeaderAndLabel.比例' | translate"
              [formControlName]="'check_radio'"></nz-input-number>
          </nz-input-number-group>
        </nz-form-control>
        <nz-form-control *ngIf="parentGroup.get('check_type')?.value === checkTypeEnum.allCheck">
          {{ parentGroup.get('check_radio')?.value + '%' }}
        </nz-form-control>
      </nz-form-item>
    </ng-container>
    <ng-container *ngIf="editMode !== 'read'">
      <nz-form-item class="form-item form-item-plant" style="margin-right: 24px">
        <nz-form-label class="info-label">{{ 'outsourcingTableHeaderAndLabel.跟单员' | translate }} </nz-form-label>
        <nz-form-control>
          <!-- <nz-select
            [formControlName]="'merchandiser_id'"
            [compareWith]="compareFn"
            [nzPlaceHolder]="'placeholder.select' | translate"
            nzAllowClear>
            <nz-option [nzValue]="item" [nzLabel]="item.label" *ngFor="let item of merchandiserOptions"></nz-option>
          </nz-select> -->
          <flc-text-truncated [data]="parentGroup.get('merchandiser_name')?.value ?? null"></flc-text-truncated>
        </nz-form-control>
      </nz-form-item>
    </ng-container>
    <!-- 检验方式只读 -->
    <nz-form-item class="form-item form-item-plant" *ngIf="editMode === 'read'">
      <nz-form-label class="info-label">{{ 'outsourcingTableHeaderAndLabel.检验方式' | translate }} </nz-form-label>
      <nz-form-control>
        <div
          *ngIf="parentGroup.get('check_type')?.value !== 0; else checkTypeTpl"
          class="check-type-read"
          [ngClass]="{
            'select-round-color-1': parentGroup.get('check_type')?.value === checkTypeEnum.allCheck,
            'select-round-color-2': parentGroup.get('check_type')?.value === checkTypeEnum.partCheck,
            'select-round-color-3': parentGroup.get('check_type')?.value === checkTypeEnum.noCheck
          }">
          {{
            check_type_text[parentGroup.get('check_type')?.value] +
              (!flcUtilService.isNilOrEmptyStr(parentGroup.get('check_radio')?.value) && parentGroup.get('check_radio')?.value !== '0'
                ? ' ' + parentGroup.get('check_radio')?.value + '%'
                : '')
          }}
        </div>
        <ng-template #checkTypeTpl>
          <flc-text-truncated [data]="null"></flc-text-truncated>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <!-- 跟单员只读 -->
    <nz-form-item class="form-item form-item-plant" *ngIf="editMode === 'read'">
      <nz-form-label class="info-label">{{ 'outsourcingTableHeaderAndLabel.跟单员' | translate }} </nz-form-label>
      <nz-form-control>
        <flc-text-truncated [data]="parentGroup.get('merchandiser_name')?.value ?? null"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>
    <ng-container *ngIf="editMode !== 'read'">
      <nz-form-item class="form-item form-item-plant" style="margin-right: 4px">
        <nz-form-label class="info-label">QC </nz-form-label>
        <nz-form-control>
          <!-- <nz-select [formControlName]="'qc_id'" [compareWith]="compareFn" [nzPlaceHolder]="'placeholder.select' | translate" nzAllowClear>
            <nz-option [nzValue]="item" [nzLabel]="item.label" *ngFor="let item of merchandiserOptions"></nz-option>
          </nz-select> -->
          <flc-text-truncated [data]="parentGroup.get('qc')?.value ?? null"></flc-text-truncated>
        </nz-form-control>
      </nz-form-item>
    </ng-container>
    <!-- QC只读 -->
    <nz-form-item class="form-item form-item-plant" *ngIf="editMode === 'read'">
      <nz-form-label class="info-label">QC </nz-form-label>
      <nz-form-control>
        <flc-text-truncated [data]="parentGroup.get('qc')?.value ?? null"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>

    <!-- 生产计划起止 -->
    <nz-form-item class="form-item form-item-plant">
      <nz-form-label class="info-label">生产计划起止</nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'">
        <nz-range-picker
          nzFormat="yyyy-MM-dd"
          formControlName="plan_time"
          style="width: 240px"
          [nzDisabled]="io_basic?.is_use_plan"></nz-range-picker>
      </nz-form-control>
      <nz-form-control *ngIf="editMode === 'read'">
        <flc-text-truncated
          data="{{
            parentGroup.get('plan_time')?.value?.length
              ? (parentGroup.get('plan_time')?.value[0] | date: 'yyyy-MM-dd') +
                '~' +
                (parentGroup.get('plan_time')?.value[1] | date: 'yyyy-MM-dd')
              : null
          }}"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>
    <!-- 备注 -->
    <nz-form-item class="form-item form-item-plant">
      <nz-form-label class="info-label">备注</nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'">
        <input class="remark-input" nzAllowClear nz-input [formControlName]="'remark'" placeholder="请输入" />
      </nz-form-control>
      <nz-form-control *ngIf="editMode === 'read'">
        <flc-text-truncated [data]="parentGroup.get('remark')?.value"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>

    <!-- 预付款比例 -->
    <nz-form-item class="form-item form-item-plant">
      <nz-form-label class="info-label">预付款比例</nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="预付款比例">
        <nz-input-number-group nzAddOnAfter="%">
          <nz-input-number
            flcStringifyFmCtrlValue
            [nzPrecision]="2"
            [nzMin]="0"
            [nzMax]="*********.99"
            [formControlName]="'advance_payment_ratio'"
            [nzPlaceHolder]="'placeholder.input' | translate"
            (ngModelChange)="changePrepaymentsProportion($event)"></nz-input-number>
        </nz-input-number-group>
      </nz-form-control>
      <nz-form-control *ngIf="editMode === 'read'">
        <flc-text-truncated
          [data]="
            parentGroup.get('advance_payment_ratio')?.value ? parentGroup.get('advance_payment_ratio')?.value + '%' : ''
          "></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>

    <!-- 预付款金额 -->
    <nz-form-item class="form-item form-item-plant">
      <nz-form-label class="info-label">预付款金额</nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="预付款金额">
        <nz-input-number
          flcStringifyFmCtrlValue
          style="width: 160px"
          [nzPrecision]="2"
          [nzMin]="0"
          [nzMax]="*********.99"
          [formControlName]="'advance_payment_money'"
          [nzPlaceHolder]="'placeholder.input' | translate"
          (ngModelChange)="changePrepaymentsAmount($event)"></nz-input-number>
      </nz-form-control>
      <nz-form-control *ngIf="editMode === 'read'">
        <flc-text-truncated [data]="parentGroup.get('advance_payment_money')?.value"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>
    <!-- 付款方式 -->
    <nz-form-item class="form-item form-item-plant">
      <nz-form-label class="info-label">付款方式</nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="付款方式">
        <flc-dynamic-search-select
          [optAlwaysReload]="true"
          [dataUrl]="dictOptionsUrl"
          [column]="'payment_method'"
          [formControlName]="'payment_method_id'"
          [defaultValue]="{ value: parentGroup.get('payment_method_id')?.value, label: parentGroup.get('payment_method')?.value }"
          (handleSearch)="dynamicSearchChange($event, 'payment_method_id')">
        </flc-dynamic-search-select>
      </nz-form-control>
      <nz-form-control *ngIf="editMode === 'read'">
        <flc-text-truncated [data]="parentGroup.get('payment_method')?.value"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>
    <!-- 结算方式 -->
    <nz-form-item class="form-item form-item-plant">
      <nz-form-label class="info-label">结算方式</nz-form-label>
      <nz-form-control *ngIf="editMode !== 'read'" flcErrorTip="结算方式">
        <flc-dynamic-search-select
          [optAlwaysReload]="true"
          [dataUrl]="dictOptionsUrl"
          [column]="'settlement_method'"
          [formControlName]="'settlement_method_id'"
          [defaultValue]="{ value: parentGroup.get('settlement_method_id')?.value, label: parentGroup.get('settlement_method')?.value }"
          (handleSearch)="dynamicSearchChange($event, 'settlement_method_id')">
        </flc-dynamic-search-select>
      </nz-form-control>
      <nz-form-control *ngIf="editMode === 'read'">
        <flc-text-truncated [data]="parentGroup.get('settlement_method')?.value"></flc-text-truncated>
      </nz-form-control>
    </nz-form-item>
  </div>
  <div class="plant-item-header-right">
    <ng-container *ngIf="editMode == 'read' && sample_show_factory[this.parentGroup?.value?.factory_code]">
      <button nz-button flButton="pretty-primary" [nzShape]="'round'" [flcDisableOnClick]="1000" (click)="autoSample()">
        重新自动打样
      </button>
      <nz-divider nzType="vertical"></nz-divider>
    </ng-container>

    <!-- 只在有权限、状态为审核通过、已接单情况下展示 -->
    <ng-container
      *ngIf="
        showOrderFinish &&
        orderDetail?.status === orderStatusEnum.auditPass &&
        parentGroup.get('deletable')?.value === false &&
        parentGroup.get('finish')?.value !== true
      ">
      <button nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="orderFinish.emit()" [flcDisableOnClick]="1000">
        {{ 'outsourcingComponents.订单完成' | translate }}
      </button>
      <nz-divider nzType="vertical"></nz-divider>
    </ng-container>

    <ng-container *ngIf="showAdvancePayment && orderDetail?.status === orderStatusEnum.auditPass">
      <button nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="onAdvancePayment.emit()" [flcDisableOnClick]="1000">
        {{ 'outsourcingComponents.预付款' | translate }}
      </button>
      <nz-divider nzType="vertical"></nz-divider>
    </ng-container>

    <!-- 一键入库：受权限控制 且审核通过 -->
    <ng-container *ngIf="orderDetail?.status === orderStatusEnum.auditPass && showOneClickInbound">
      <button nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="oneClickInbound.emit()" [flcDisableOnClick]="1000">
        {{ 'outsourcingComponents.一键入库' | translate }}
      </button>
      <nz-divider nzType="vertical"></nz-divider>
    </ng-container>

    <!-- 删除加工厂 -->
    <i
      *ngIf="editMode !== 'read' && !io_basic?.is_use_plan"
      nz-tooltip
      nz-icon
      [nzIconfont]="'icon-caozuolan_shanchu1'"
      class="icon-delete"
      [nzTooltipTitle]="'outsourcingMessage.删除加工厂' | translate"
      (click)="_handleRemoveFactory()"></i>
    <div *ngIf="editMode !== 'read'"><nz-divider nzType="vertical"></nz-divider></div>
    <div class="toggle" (click)="_handleChangeToggle()">
      <span>{{ (toggle ? 'outsourcingComponents.收起' : 'outsourcingComponents.展开') | translate }}</span>
      <i nz-icon [nzIconfont]="toggle ? 'icon-shouqi1' : 'icon-zhankai2'" class="icon-toggle"></i>
    </div>
  </div>
</div>
<!-- 二次工艺外发 -->
<div
  *ngIf="factory_type === 2"
  class="plant-item-header"
  [ngClass]="{ 'plant-item-header-line': toggle, 'reset-padding': !toggle }"
  [hidden]="editMode === 'read' && _pos.length === 0 && !parentGroup.value['factory_short_name']">
  <div nz-row class="plant-item-header-left" [formGroup]="parentGroup">
    <nz-form-item class="form-item form-item-plant">
      <nz-form-label class="info-label">{{ 'outsourcingTableHeaderAndLabel.外发厂' | translate }} </nz-form-label>
      <nz-form-control
        *ngIf="editMode !== 'read'"
        style="max-width: 300px"
        [flcErrorTip]="'outsourcingTableHeaderAndLabel.外发厂' | translate">
        <flc-dynamic-search-select
          class="plant-select-width"
          [disabled]="parentGroup.get('deletable')?.value === false"
          [canClear]="false"
          [dataUrl]="searchOptionFetchUrl"
          [transData]="{ value: 'label', label: 'label' }"
          [defaultValue]="{ label: parentGroup.value['factory_short_name'], value: parentGroup.value['factory_short_name'] }"
          [formControlName]="'factory_short_name'"
          [column]="'factory_name'"
          [minWidth]="160"
          [payLoad]="{ factory_type: factory_type }"
          (handleSearch)="_handleChangeValue($event)">
        </flc-dynamic-search-select>
      </nz-form-control>
      <flc-text-truncated *ngIf="editMode === 'read'" [data]="parentGroup.value['factory_short_name']"></flc-text-truncated>
    </nz-form-item>
  </div>
  <div class="plant-item-header-right">
    <div
      class="status-area-2"
      *ngIf="[orderStatusEnum.toModifyAudit, orderStatusEnum.auditPass, orderStatusEnum.modifyAuditReturn].includes(orderStatus)">
      <label>{{ 'outsourcingComponents.状态' | translate }}：</label>
      <div class="plant-status-box" [ngClass]="{ 'is-active': parentGroup.get('deletable')?.value === false }">
        <ng-container *ngIf="parentGroup.get('deletable')?.value === false; else plantStatusTpl">
          <div class="icon-chenggong-box">
            <i class="plant-status-icon" nz-icon [nzIconfont]="'icon-dui1'"></i>
          </div>
          <div class="plant-status-text">{{ 'outsourcingComponents.已接单' | translate }}</div>
        </ng-container>
        <ng-template #plantStatusTpl>
          <i class="plant-status-icon" nz-icon [nzIconfont]="'icon-jianshao'"></i>
          <div class="plant-status-text">{{ 'outsourcingComponents.未接单' | translate }}</div>
        </ng-template>
      </div>
      <div><nz-divider nzType="vertical"></nz-divider></div>
    </div>
    <!-- 删除加工厂 -->
    <i
      *ngIf="editMode !== 'read'"
      nz-tooltip
      nz-icon
      [nzIconfont]="'icon-caozuolan_shanchu1'"
      class="icon-delete"
      [nzTooltipTitle]="'outsourcingMessage.删除加工厂' | translate"
      (click)="_handleRemoveFactory()"></i>
    <div *ngIf="editMode !== 'read'"><nz-divider nzType="vertical"></nz-divider></div>
    <div class="toggle" (click)="_handleChangeToggle()">
      <span>{{ (toggle ? 'outsourcingComponents.收起' : 'outsourcingComponents.展开') | translate }}</span>
      <i nz-icon [nzIconfont]="toggle ? 'icon-shouqi1' : 'icon-zhankai2'" class="icon-toggle"></i>
    </div>
  </div>
</div>
